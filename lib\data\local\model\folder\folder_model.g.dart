// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'folder_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class FolderModelAdapter extends TypeAdapter<FolderModel> {
  @override
  final int typeId = 6;

  @override
  FolderModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return FolderModel(
      id: fields[0] == null ? '' : fields[0] as String?,
      folderName: fields[1] == null ? '' : fields[1] as String,
      backendId: fields[2] == null ? '' : fields[2] as String,
      icon: fields[3] == null ? '' : fields[3] as String?,
      createdAt: fields[4] == null ? '' : fields[4] as String?,
      updatedAt: fields[5] == null ? '' : fields[5] as String?,
      parentFolderId: fields[6] as String?,
      subfolders:
          fields[7] == null ? [] : (fields[7] as List?)?.cast<FolderModel>(),
      path: fields[8] == null ? '' : fields[8] as String?,
      level: fields[9] == null ? 0 : fields[9] as int?,
      noteCount: fields[10] == null ? 0 : fields[10] as int?,
      notes: fields[11] == null ? [] : (fields[11] as List?)?.cast<NoteModel>(),
    );
  }

  @override
  void write(BinaryWriter writer, FolderModel obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.folderName)
      ..writeByte(2)
      ..write(obj.backendId)
      ..writeByte(3)
      ..write(obj.icon)
      ..writeByte(4)
      ..write(obj.createdAt)
      ..writeByte(5)
      ..write(obj.updatedAt)
      ..writeByte(6)
      ..write(obj.parentFolderId)
      ..writeByte(7)
      ..write(obj.subfolders)
      ..writeByte(8)
      ..write(obj.path)
      ..writeByte(9)
      ..write(obj.level)
      ..writeByte(10)
      ..write(obj.noteCount)
      ..writeByte(11)
      ..write(obj.notes);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FolderModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
