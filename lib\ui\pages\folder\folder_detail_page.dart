import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:lottie/lottie.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/folder/folder_tracking_helper.dart';
import 'package:note_x/ui/pages/folder/widget/folder_detail_list_view.dart';
import 'package:note_x/ui/pages/folder/widget/folder_detail_list_view_data.dart';
import '../../../base/base_page_state.dart';

class FolderDetailPage extends StatefulWidget {
  const FolderDetailPage({
    super.key,
    required this.folder,
    this.isUnsyncedFolder = false,
  });

  final FolderModel folder;
  final bool isUnsyncedFolder;

  @override
  State<FolderDetailPage> createState() => _FolderDetailPageState();
}

class _FolderDetailPageState
    extends BasePageStateDelegate<FolderDetailPage, FolderDetailCubit> {
  TextEditingController editFolderController = TextEditingController();

  @override
  void initState() {
    super.initState();
    cubit.initData(widget.folder);
  }

  /// Builds the loading widget
  Widget _buildLoadingWidget() {
    return Center(
      child: Lottie.asset(
        Assets.videos.commonLoading,
        width: 50.w,
        height: 50.h,
      ),
    );
  }

  /// Builds the empty state widget when no notes are found
  Widget _buildEmptyStateWidget() {
    return Center(
      child: Column(
        children: [
          AppConstants.kSpacingItem48,
          SvgPicture.asset(
            Assets.icons.icEmptyNoteNotexEmpty,
            width: 160.w,
            height: 160.h,
          ),
          AppConstants.kSpacingItem12,
          CommonText(
            S.current.no_notes_in_folder,
            style: TextStyle(
              fontSize: cubit.appCubit.isTablet ? 18 : 16.sp,
              fontWeight: FontWeight.w600,
              color: context.colorScheme.mainPrimary,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the notes list widget
  Widget _buildNotesListWidget(List<NoteModel> notes) {
    return HomeItemNoteListView(
      listNote: notes,
    );
  }

  /// Builds the error state widget
  Widget _buildErrorStateWidget() {
    return const Center(child: CommonText('No notes found'));
  }

  /// Builds body content for unsynced folder
  Widget _buildUnsyncedFolderContent() {
    return ValueListenableBuilder<Box<FolderModel>>(
      valueListenable: HiveService().folderBox.listenable(),
      builder: (context, noteBox, child) {
        return FutureBuilder<List<NoteModel>>(
          future: HiveFolderService.getNotesFailed(userId: cubit.userId),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return _buildLoadingWidget();
            } else if (snapshot.hasData) {
              if (snapshot.data == null || snapshot.data!.isEmpty) {
                return _buildEmptyStateWidget();
              } else {
                return _buildNotesListWidget(snapshot.data!);
              }
            } else {
              return _buildErrorStateWidget();
            }
          },
        );
      },
    );
  }

  /// Builds body content for regular folder using BlocBuilder
  Widget _buildRegularFolderContent() {
    // You can still use your existing data fetching logic here
    return ValueListenableBuilder<Box<FolderModel>>(
      valueListenable: HiveService().folderBox.listenable(),
      builder: (context, folderBox, child) {
        final folderModel = HiveFolderService.getFolderById(
            folderBackendId: widget.folder.backendId);
        return FolderDetailListView(
            viewData: FolderDetailListViewData(
                folders: folderModel?.subfolders ?? [], notes: []));
      },
    );
  }

  /// Builds the main body content with notes
  Widget _buildBodyContent() {
    if (widget.isUnsyncedFolder) {
      return _buildUnsyncedFolderContent();
    } else {
      return _buildRegularFolderContent();
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: AppBarWidget(
        title: widget.folder.folderName,
        onPressed: () {
          FolderTrackingHelper.onFolderDetailBack();
          Navigator.pop(context);
        },
        actions: [
          widget.isUnsyncedFolder
              ? const SizedBox()
              : Padding(
                  padding: EdgeInsets.only(right: 8.w),
                  child: FolderMoreOptions(
                    folder: widget.folder,
                    editFolderController: editFolderController,
                    icon: Assets.icons.icMore,
                    isInDetailPage: true,
                  ),
                ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: _buildBodyContent(),
          ),
        ],
      ),
    );
  }
}
